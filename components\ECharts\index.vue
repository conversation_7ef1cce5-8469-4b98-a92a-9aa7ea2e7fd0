<!-- echarts.vue -->
<template>
  <view class="echarts-container" :style="{ height: height + 'rpx' }">
    <view
      :id="canvasId"
      ref="chartContainer"
      class="echarts-view"
      :style="{
        height: height + 'rpx',
        width: '100%',
      }"
      :canvas-id="canvasId"
      :change:canvas-id="echartsRender.changeCanvasId"
      :data-canvas-id="canvasId"
      :data-chart-data="JSON.stringify(chartData)"
      :data-opts="JSON.stringify(opts)"
      :data-chart-type="chartType"
    >
    </view>
  </view>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';

  // 定义 props
  const props = defineProps({
    chartType: {
      type: String,
      default: 'line',
    },
    chartData: {
      type: Object,
      required: true,
    },
    opts: {
      type: Object,
      default: () => ({}),
    },
    canvasId: {
      type: String,
      required: true,
    },
    height: {
      type: Number,
      default: 300,
    },
  });
</script>

<script module="echartsRender" lang="renderjs">
  module.exports = {
    props: ['canvasId', 'chartData', 'opts', 'chartType'],
    data() {
      return {
        chart: null,
        isInitialized: false,
        echartsInstance: null,
        containerNode: null,
        currentCanvasId: null,
        retryCount: 0,
        maxRetries: 5,
        isLoading: false,
        lastOption: null,
        ipv6CheckTimer: null
      }
    },

    mounted() {
      if (this.canvasId) {
        this.currentCanvasId = this.canvasId
        const container = document.querySelector(`#${this.currentCanvasId}`)
        if (container) {
          this.initWithRetry()
        }
      }

      // 监听属性变化
      this.observeChanges()
    },

    beforeDestroy() {
      // 清理IPv6检查定时器
      if (this.ipv6CheckTimer) {
        clearInterval(this.ipv6CheckTimer)
        this.ipv6CheckTimer = null
      }

      // 清理图表实例
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },

    methods: {
      changeCanvasId(newVal) {
        if (newVal) {
          this.currentCanvasId = newVal
          this.initECharts()
        }
      },
      
      observeChanges() {
        if (!this.currentCanvasId) return

        const container = document.querySelector(`#${this.currentCanvasId}`)
        if (!container) {
          console.warn(`无法找到容器元素: ${this.currentCanvasId}`)
          return
        }

        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'attributes') {
              if (mutation.attributeName === 'data-chart-data' && this.chart) {
                try {
                  const chartData = JSON.parse(container.getAttribute('data-chart-data') || '{}')
                  this.updateChartWithData(chartData)
                } catch (e) {
                  console.error('解析图表数据失败:', e)
                  // 对于IPv6相关的图表，尝试强制刷新
                  if (this.currentCanvasId.includes('ipv6')) {
                    this.forceRefreshChart()
                  }
                }
              }
              if (mutation.attributeName === 'data-opts' && this.chart) {
                try {
                  const opts = JSON.parse(container.getAttribute('data-opts') || '{}')
                  this.updateChartWithOptions(opts)
                } catch (e) {
                  console.error('解析配置数据失败:', e)
                  // 对于IPv6相关的图表，尝试强制刷新
                  if (this.currentCanvasId.includes('ipv6')) {
                    this.forceRefreshChart()
                  }
                }
              }
            }
          })
        })

        observer.observe(container, {
          attributes: true,
          attributeFilter: ['data-chart-data', 'data-opts'],
          subtree: false,
          childList: false
        })

        // 为IPv6图表添加额外的监听机制
        if (this.currentCanvasId.includes('ipv6')) {
          this.setupIPv6SpecialHandling(container)
        }
      },

      // IPv6图表的特殊处理
      setupIPv6SpecialHandling(container) {
        // 使用定时器定期检查IPv6图表的数据变化
        if (this.ipv6CheckTimer) {
          clearInterval(this.ipv6CheckTimer)
        }

        let lastDataHash = ''

        this.ipv6CheckTimer = setInterval(() => {
          if (!this.chart || !container) return

          try {
            const currentData = container.getAttribute('data-chart-data') || '{}'
            const currentHash = this.simpleHash(currentData)

            if (currentHash !== lastDataHash) {
              lastDataHash = currentHash
              const chartData = JSON.parse(currentData)
              this.updateChartWithData(chartData)
            }
          } catch (e) {
            console.error('IPv6图表数据检查失败:', e)
          }
        }, 1000) // 每秒检查一次
      },

      // 强制刷新图表
      forceRefreshChart() {
        if (!this.chart) return

        try {
          // 重新初始化图表
          setTimeout(() => {
            if (this.chart) {
              this.chart.resize()
              // 如果有最后的配置，重新应用
              if (this.lastOption) {
                this.chart.setOption(this.lastOption, true)
              }
            }
          }, 100)
        } catch (e) {
          console.error('强制刷新图表失败:', e)
        }
      },

      // 简单哈希函数
      simpleHash(str) {
        let hash = 0
        if (str.length === 0) return hash.toString()

        for (let i = 0; i < str.length; i++) {
          const char = str.charCodeAt(i)
          hash = ((hash << 5) - hash) + char
          hash = hash & hash
        }

        return Math.abs(hash).toString(36)
      },

      async initWithRetry() {
        if (this.isLoading) return

        this.isLoading = true
        try {
          // 等待一段时间，确保 DOM 已经渲染
          await new Promise(resolve => setTimeout(resolve, 200))

          if (!this.currentCanvasId) {
            if (this.retryCount < this.maxRetries) {
              this.retryCount++
              setTimeout(() => this.initWithRetry(), 500)
            }
            return
          }

          await this.initECharts()
        } finally {
          this.isLoading = false
        }
      },

      async initECharts() {
        try {
          // 动态加载 echarts
          const echarts = await this.loadECharts()
          if (!echarts) {
            throw new Error('加载 ECharts 失败')
          }

          this.echartsInstance = echarts

          // 获取容器元素
          const container = document.querySelector(`#${this.currentCanvasId}`)
          if (!container) {
            throw new Error(`找不到容器元素: ${this.currentCanvasId}`)
          }

          // 获取容器尺寸
          await new Promise(resolve => setTimeout(resolve, 100))
          const rect = container.getBoundingClientRect()
          const width = rect.width || container.clientWidth || 300
          const height = rect.height || container.clientHeight || 300

          if (!width || !height) {
            throw new Error('容器尺寸无效')
          }

          // 保存容器节点
          this.containerNode = container

          // 初始化 echarts 实例
          if (this.chart) {
            this.chart.dispose()
          }

          this.chart = echarts.init(container)

          // 配置图表
          await this.initChart()

          this.isInitialized = true
          this.retryCount = 0

        } catch (error) {
          console.error('ECharts 初始化失败:', error)
          if (this.retryCount < this.maxRetries) {
            this.retryCount++
            setTimeout(() => this.initWithRetry(), 500)
          }
        }
      },

      async loadECharts() {
        if (window.echarts) {
          return window.echarts
        }

        try {
          const paths = [
            './static/echarts/echarts.min.js',
            '/static/echarts/echarts.min.js',
            '../../../static/echarts/echarts.min.js'
          ]

          let lastScript = null
          for (const path of paths) {
            try {
              const echartsScript = document.createElement('script')
              echartsScript.src = path
              lastScript = echartsScript

              const result = await new Promise((resolve) => {
                echartsScript.onload = () => {
                  resolve(true)
                }
                echartsScript.onerror = () => {
                  resolve(false)
                }
                document.head.appendChild(echartsScript)
              })

              if (result && window.echarts) {
                return window.echarts
              }

              // 如果加载失败，移除script标签
              try {
                if (echartsScript.parentNode) {
                  echartsScript.parentNode.removeChild(echartsScript)
                }
              } catch (e) {
                console.warn('移除script标签失败:', e)
              }
            } catch (error) {
              console.error('尝试路径失败:', path, error)
            }
          }

          // 如果所有本地路径都失败了，尝试从 CDN 加载
          const cdnScript = document.createElement('script')
          cdnScript.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js'

          const cdnResult = await new Promise((resolve) => {
            cdnScript.onload = () => {
              resolve(true)
            }
            cdnScript.onerror = () => {
              resolve(false)
            }
            document.head.appendChild(cdnScript)
          })

          if (cdnResult && window.echarts) {
            return window.echarts
          }

          console.error('所有加载方式都失败了')
          return null
        } catch (error) {
          console.error('加载echarts.js时发生错误:', error)
          return null
        }
      },
      
      async initChart() {
        if (!this.chart) return;
        
        try {
          if (!this.currentCanvasId) return;
          const container = document.querySelector(`#${this.currentCanvasId}`);
          if (!container) return;
          
          let chartData = {};
          let userOpts = {};
          
          try {
            chartData = JSON.parse(container.getAttribute('data-chart-data') || '{}');
            userOpts = JSON.parse(container.getAttribute('data-opts') || '{}');
          } catch (e) {
            console.error('数据解析失败:', e);
            return;
          }
          
          // 获取图表类型
          const chartType = container.getAttribute('data-chart-type') || 'line';
          
          // 生成基础配置
          const baseOption = this.generateBaseOption(chartData, chartType);
          
          // 合并用户配置
          const option = this.deepMerge(baseOption, userOpts);
          
          // 保存最终配置
          this.lastOption = option;
          
          // 设置图表选项
          await this.chart.setOption(option, true);
          
          // 适应容器尺寸
          this.chart.resize();
        } catch (error) {
          console.error('初始化图表失败:', error);
        }
      },
      
      // 处理数据更新
      async updateChartWithData(chartData) {
        if (!this.chart) return;
        
        try {
          const container = document.querySelector(`#${this.currentCanvasId}`);
          if (!container) return;
          
          const chartType = container.getAttribute('data-chart-type') || 'line';
          
          // 仪表盘特殊处理
          if (chartType === 'gauge') {
            // 仪表盘数据更新需要特殊处理以保留动画效果
            if (chartData.series && Array.isArray(chartData.series) && 
                chartData.series[0] && chartData.series[0].data && 
                chartData.series[0].data[0]) {
              
              // 获取新的数据配置
              const newDataItem = chartData.series[0].data[0];
              
              // 获取当前配置
              const currentOption = this.chart.getOption();
              
              // 更新值和其他配置
              if (currentOption.series && 
                  currentOption.series[0] && 
                  currentOption.series[0].data && 
                  currentOption.series[0].data[0]) {
                
                // 合并原有配置和新配置，确保新的颜色等属性生效
                const newData = {...currentOption.series[0].data[0], ...newDataItem};
                
                // 设置更新，确保valueAnimation生效并应用新的配置
                this.chart.setOption({
                  series: [{
                    data: [newData],
                    // 同时更新系列级别的配置
                    ...chartData.series[0]
                  }]
                }, false);
                
                // 仪表盘处理结束，提前返回
                return;
              }
            }
          }
          
          // 其他图表类型或无法单独更新数据的情况
          let userOpts = {};
          try {
            userOpts = JSON.parse(container.getAttribute('data-opts') || '{}');
          } catch (e) {
            console.error('解析用户配置失败:', e);
          }
          
          let option;
          if (Object.keys(userOpts).length > 0) {
            // 有外部配置，使用外部配置
            option = { ...userOpts };
            
            // 如果是需要数据更新，合并数据部分
            if (chartData.series && Array.isArray(chartData.series)) {
              if (!option.series) option.series = [];
              
              chartData.series.forEach((item, index) => {
                if (!option.series[index]) {
                  option.series[index] = { type: chartType };
                }
                if (item.data) {
                  option.series[index].data = item.data;
                }
              });
            }
          } else {
            // 没有外部配置时使用基础配置
            option = this.generateBaseOption(chartData, chartType);
          }
          
          // 保存最终配置
          this.lastOption = option;
          
          // 更新图表，使用合并模式保留动画
          this.chart.setOption(option, false);
          
          // 适应容器尺寸
          this.chart.resize();
        } catch (error) {
          console.error('更新图表数据失败:', error);
        }
      },
      
      // 处理选项更新
      async updateChartWithOptions(opts) {
        if (!this.chart) return;
        
        try {
          // 直接使用外部提供的配置
          if (Object.keys(opts).length > 0) {
            const chartType = document.querySelector(`#${this.currentCanvasId}`)?.getAttribute('data-chart-type') || 'line';
            
            // 保存最终配置
            this.lastOption = opts;
            
            // 仪表盘特殊处理
            if (chartType === 'gauge') {
              // 确保动画参数正确
              if (opts.animation === undefined) opts.animation = true;
              if (opts.animationDuration === undefined) opts.animationDuration = 1000;
              if (opts.animationEasing === undefined) opts.animationEasing = 'cubicInOut';
              
              // 确保每个系列中的detail都有valueAnimation
              if (opts.series && Array.isArray(opts.series)) {
                opts.series.forEach(serie => {
                  if (serie.type === 'gauge') {
                    // 确保detail存在并有valueAnimation
                    if (!serie.detail) serie.detail = {};
                    serie.detail.valueAnimation = true;
                    
                    // 确保每个数据项的detail也有valueAnimation
                    if (serie.data && Array.isArray(serie.data)) {
                      serie.data.forEach(dataItem => {
                        if (dataItem && typeof dataItem === 'object') {
                          if (!dataItem.detail) dataItem.detail = {};
                          dataItem.detail.valueAnimation = true;
                        }
                      });
                    }
                  }
                });
              }
              
              // 确保使用合并模式更新
              this.chart.setOption(opts, false);
            } else {
              // 其他图表类型正常处理
              this.chart.setOption(opts, false);
            }
          }
          
          // 适应容器尺寸
          this.chart.resize();
        } catch (error) {
          console.error('更新图表选项失败:', error);
        }
      },
      
      // 生成基础配置
      generateBaseOption(data, chartType) {
        const { categories = [], series = [], color = [] } = data;
        
        // 数据校验
        if (!Array.isArray(series)) {
          console.error('series 不是数组:', series);
          return {};
        }
        
        // 动画配置 - 优化首屏加载体验
        const animationConfig = {
          animation: true,
          animationDuration: 800, // 与首屏加载延迟时间匹配
          animationEasing: 'cubicInOut',
          animationThreshold: 10,
          // 添加延迟动画，确保图表渲染更平滑
          animationDelay: 0,
          animationDurationUpdate: 600
        };
        
        // 根据图表类型生成基础配置
        if (chartType === 'pie' || chartType === 'gauge') {
          // 饼图/环形图/仪表盘配置
          return {
            ...animationConfig,
            grid: {
              top: 0,
              left: 0,
              right: 0,
              bottom: 0
            },
            series: series.map(item => ({
              type: chartType,
              center: ['50%', '50%'],
              ...animationConfig,
              ...item
            }))
          };
        } else {
          // 其他图表类型配置
          return {
            ...animationConfig,
            color,
            grid: {
              top: 30,
              right: 5,
              bottom: 20,
              left: 40,
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'line'
              }
            },
            legend: {
              top: 5,
              left: 'center'
            },
            xAxis: {
              type: 'category',
              data: categories,
              boundaryGap: false,
              axisLabel: {
                interval: 0,
              },
            },
            yAxis: {
              type: 'value',
              scale: true
            },
            series: series.map(item => ({
              ...item,
              name: item.name,
              type: chartType,
              data: item.data
            }))
          };
        }
      },
      
      // 更可靠的深度合并函数
      deepMerge(target, source) {
        if (!source) return target;
        if (!target) return source;
        
        const result = { ...target };
        
        Object.keys(source).forEach(key => {
          if (source[key] !== null && typeof source[key] === 'object') {
            // 处理数组特殊情况
            if (Array.isArray(source[key])) {
              // 如果目标也是数组，对于series等特殊配置，应该进行项合并而不是直接替换
              if (key === 'series' && Array.isArray(target[key])) {
                result[key] = target[key].map((item, index) => {
                  return source[key][index] ? this.deepMerge(item, source[key][index]) : item;
                });
                
                // 处理源数组比目标数组长的情况
                if (source[key].length > target[key].length) {
                  for (let i = target[key].length; i < source[key].length; i++) {
                    result[key].push(source[key][i]);
                  }
                }
              } else {
                // 其他数组直接替换
                result[key] = source[key];
              }
            } else if (key in target) {
              // 对象递归合并
              result[key] = this.deepMerge(target[key], source[key]);
            } else {
              // 目标没有此键，直接添加
              result[key] = source[key];
            }
          } else {
            // 基本类型直接替换
            result[key] = source[key];
          }
        });
        
        return result;
      }
    }
  }
</script>

<style>
  .echarts-container {
    width: 100%;
    position: relative;
    box-sizing: border-box;
    background-color: transparent;
    padding: 0;
    margin: 0;
    overflow: visible;
  }

  .echarts-view {
    width: 100%;
    height: 100%;
    display: block;
    box-sizing: border-box;
    background-color: transparent;
    border-radius: 0;
    box-shadow: none;
    overflow: visible;
    position: relative;
    padding: 0;
    margin: 0;
  }
</style>
